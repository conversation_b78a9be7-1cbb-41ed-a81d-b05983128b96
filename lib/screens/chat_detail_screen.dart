import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/models/user_model.dart';
import 'package:hia_sang_ma/services/chat_service.dart';
import 'package:hia_sang_ma/services/auth_service.dart';
import 'package:hia_sang_ma/services/socket_service.dart';
import 'package:hia_sang_ma/services/chat_socket_service.dart';
import 'package:hia_sang_ma/services/unread_message_service.dart';
import 'package:hia_sang_ma/widgets/message_types/message_content_widget.dart';

class ChatDetailScreen extends StatefulWidget {
  final ChatModel chat;

  const ChatDetailScreen({super.key, required this.chat});

  @override
  State<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends State<ChatDetailScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ChatService _chatService = ChatService();
  final AuthService _authService = AuthService();
  final SocketService _socketService = SocketService();
  final ChatSocketService _chatSocketService = ChatSocketService();
  final UnreadMessageService _unreadMessageService = UnreadMessageService();

  List<ChatMessage> _messages = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _isFirstLoad = true;
  String? _errorMessage;
  int _currentUserId = 0;
  UserModel? _currentUser;
  int _currentPage = 1;
  static const int _messagesPerPage = 20;

  // Socket.IO related state
  bool _isSocketConnected = false;
  StreamSubscription<bool>? _connectionSubscription;
  StreamSubscription<ChatMessage>? _newMessageSubscription;
  StreamSubscription<String>? _errorSubscription;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    _loadMessages();
    _scrollController.addListener(_onScroll);
    _messageController.addListener(_onMessageTextChanged);
    _initializeSocketConnection();
    _setupSocketListeners();

    // Mark messages as read when entering the chat
    _markMessagesAsReadOnEntry();

    // Remove the duplicate scroll attempt from initState
    // Let _loadMessages handle the initial scroll
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _cleanupSocketConnections();
    super.dispose();
  }

  Future<void> _loadCurrentUser() async {
    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        setState(() {
          _currentUserId = user.id;
          _currentUser = user;
        });
      }
    } catch (e) {
      print('Error loading current user: $e');
    }
  }

  /// Mark messages as read when entering the chat room
  Future<void> _markMessagesAsReadOnEntry() async {
    try {
      // Wait a bit to ensure the user has actually entered the chat
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        await _unreadMessageService.markMessagesAsRead(chatId: widget.chat.id);
        print('Marked messages as read for chat ${widget.chat.id}');
      }
    } catch (e) {
      print('Error marking messages as read on entry: $e');
    }
  }

  Future<void> _loadMessages({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
        _currentPage = 1;
        // Don't reset _isFirstLoad on refresh - it should only be true for actual first load
      });
    }

    try {
      final messages = await _chatService.getChatMessages(
        chatId: widget.chat.id,
        page: _currentPage,
        limit: _messagesPerPage,
      );

      setState(() {
        if (isRefresh) {
          // Reverse messages to show oldest first, newest last
          _messages = messages.reversed.toList();
        } else {
          // For pagination, prepend older messages to the beginning
          _messages.insertAll(0, messages.reversed.toList());
        }
        _isLoading = false;
        _isLoadingMore = false;
      });

      // Scroll to bottom for refresh, initial load, or when adding messages to the end
      if ((isRefresh || _isFirstLoad) && messages.isNotEmpty) {
        if (isRefresh) {
          // For refresh, use smooth animation
          _scrollToBottomSmooth();
        } else {
          // For initial load, use instant scroll with retry mechanism
          _scheduleInitialScroll();
        }
      }

      // Mark that first load is complete
      if (_isFirstLoad) {
        _isFirstLoad = false;
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  void _onScroll() {
    // Load more older messages when scrolling to the top
    if (_scrollController.position.pixels ==
        _scrollController.position.minScrollExtent) {
      _loadMoreMessages();
    }
  }

  Future<void> _loadMoreMessages() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    await _loadMessages();
  }

  void _scheduleInitialScroll() {
    // Use multiple frame callbacks to ensure ListView is fully rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performScrollToBottom(isInitialLoad: true);
      });
    });
  }

  void _performScrollToBottom({bool isInitialLoad = false}) {
    if (!mounted || !_scrollController.hasClients) {
      return;
    }

    final position = _scrollController.position;
    final maxExtent = position.maxScrollExtent;

    if (maxExtent <= 0) {
      // No scrollable content yet, retry for initial load
      if (isInitialLoad) {
        Future.delayed(const Duration(milliseconds: 100), () {
          _performScrollToBottom(isInitialLoad: true);
        });
      }
      return;
    }

    if (isInitialLoad) {
      // For initial load, jump instantly to bottom
      _scrollController.jumpTo(maxExtent);

      // Schedule additional scroll attempts to handle dynamic content loading
      _scheduleScrollRetries();
    } else {
      // For refresh, use smooth animation
      _scrollController.animateTo(
        maxExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _scheduleScrollRetries() {
    // Schedule multiple scroll attempts to handle dynamic content (images, HTML)
    // that may change the scroll extent after initial render
    final retryDelays = [100, 300, 500, 1000]; // milliseconds

    for (final delay in retryDelays) {
      Future.delayed(Duration(milliseconds: delay), () {
        if (!mounted || !_scrollController.hasClients) return;

        final position = _scrollController.position;
        final maxExtent = position.maxScrollExtent;
        final currentPosition = position.pixels;

        // Only scroll if we're not already at the bottom and there's more content
        if (maxExtent > currentPosition + 10) {
          // 10px tolerance
          _scrollController.jumpTo(maxExtent);
        }
      });
    }
  }

  void _scrollToBottomSmooth() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _performScrollToBottom(isInitialLoad: false);
    });
  }

  /// Initialize Socket.IO connection with better error handling
  Future<void> _initializeSocketConnection() async {
    if (_currentUser != null) {
      try {
        print(
          '🔄 Initializing Socket.IO connection for user: ${_currentUser!.id}',
        );
        await _socketService.connect(_currentUser!.id.toString());

        // Enable debug mode (now safe - doesn't override main event listeners)
        _socketService.enableDebugMode();

        // Don't join chat room here - wait for connection confirmation
        // The join will happen in the connection listener
        print('✅ Socket.IO connection initialized successfully');
      } catch (e) {
        print('❌ Error initializing socket connection: $e');
        _showErrorSnackBar('ไม่สามารถเชื่อมต่อได้ กำลังลองใหม่...');

        // Retry connection after a delay
        Timer(const Duration(seconds: 3), () {
          if (mounted) {
            _initializeSocketConnection();
          }
        });
      }
    } else {
      print('⏳ Waiting for user data to be loaded...');
      // Retry when user is loaded
      Timer(const Duration(milliseconds: 500), () {
        if (mounted) {
          _initializeSocketConnection();
        }
      });
    }
  }

  /// Setup Socket.IO event listeners
  void _setupSocketListeners() {
    // Listen to connection status changes
    _connectionSubscription = _socketService.connectionStream.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isSocketConnected = isConnected;
        });

        if (isConnected) {
          print('🟢 Socket connected - joining chat room: ${widget.chat.id}');

          // Setup chat event listeners after connection is established
          _chatSocketService.setupChatEventListeners();

          _chatSocketService.joinChat(widget.chat.id.toString());

          // Show success message
          // ScaffoldMessenger.of(context).showSnackBar(
          //   const SnackBar(
          //     content: Text('เชื่อมต่อสำเร็จ สามารถส่งข้อความได้แล้ว'),
          //     backgroundColor: Colors.green,
          //     duration: Duration(seconds: 2),
          //   ),
          // );
        } else {
          print('🔴 Socket disconnected');
        }
      }
    });

    // Listen to socket errors
    _errorSubscription = _socketService.errorStream.listen((error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection error: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    });

    // Listen for new messages with improved handling
    _newMessageSubscription = _chatSocketService.newMessageStream.listen((
      message,
    ) {
      print('🎯 New message received in UI: ${message.content}');
      print('📊 Current chat ID: ${widget.chat.id}');
      print('📊 Message user ID: ${message.userId}');
      print('📊 Current user ID: ${_currentUser?.id}');
      print('🔗 Optimistic ID: ${message.optimisticId}');

      if (mounted) {
        // Set the correct chatId since server doesn't send it
        final messageWithChatId = message.copyWith(chatId: widget.chat.id);
        print('🔄 Processing new message...');
        _processNewMessage(messageWithChatId);
      } else {
        print('⚠️ Widget not mounted, skipping message');
      }
    });

    // Listen for message read status updates
    _chatSocketService.messagesReadStream.listen((data) {
      if (mounted && data['id']?.toString() == widget.chat.id.toString()) {
        _handleMessagesRead(data);
      }
    });

    // Listen for chat notifications
    _chatSocketService.chatNotificationStream.listen((data) {
      if (mounted) {
        _handleChatNotification(data);
      }
    });
  }

  /// Process new message received via Socket.IO with improved logic
  void _processNewMessage(ChatMessage newMessage) {
    print('🎯 _processNewMessage called');
    print('📝 New message: ${newMessage.content}');
    print('🆔 Message ID: ${newMessage.id}');
    print('👤 Message user ID: ${newMessage.userId}');
    print('� Current user ID: ${_currentUser?.id}');
    print('�🔗 Optimistic ID: ${newMessage.optimisticId}');
    print('📊 Current messages count: ${_messages.length}');

    setState(() {
      // Case 1: This is a confirmation for our own message (optimistic update)
      if (newMessage.userId == _currentUser?.id) {
        print(
          '🔄 This is our own message - checking for optimistic replacement...',
        );

        // Try to find optimistic message by optimisticId first
        int optimisticIndex = -1;
        if (newMessage.optimisticId != null) {
          optimisticIndex = _messages.indexWhere(
            (m) => m.id.toString() == newMessage.optimisticId,
          );
        }

        // If not found by optimisticId, try to find by content and user (recent message)
        if (optimisticIndex == -1) {
          optimisticIndex = _messages.lastIndexWhere(
            (m) =>
                m.userId == newMessage.userId &&
                m.content == newMessage.content &&
                m.messageStatus == MessageStatus.sending,
          );
        }

        if (optimisticIndex != -1) {
          print(
            '✅ Found optimistic message at index $optimisticIndex, updating status...',
          );
          _messages[optimisticIndex] = newMessage.copyWith(
            messageStatus: MessageStatus.delivered,
            id: newMessage.id, // Update with server-assigned ID
          );
          print('✅ Message status updated to delivered');
          return;
        } else {
          print('⚠️ Optimistic message not found, treating as new message');
        }
      }

      // Case 2: This is a new message from another user or our message not found in optimistic list
      final existingIndex = _messages.indexWhere((m) => m.id == newMessage.id);
      print(
        '🔍 Checking for existing message with ID ${newMessage.id}: ${existingIndex != -1 ? 'Found at $existingIndex' : 'Not found'}',
      );

      if (existingIndex == -1) {
        print('➕ Adding new message to list...');
        // Add new message to the end of the list
        _messages.add(
          newMessage.copyWith(messageStatus: MessageStatus.delivered),
        );
        print('✅ Message added. New count: ${_messages.length}');

        // Scroll to bottom if user is near the bottom or if it's from current user
        if (_scrollController.hasClients) {
          final position = _scrollController.position;
          final isNearBottom =
              position.pixels >= position.maxScrollExtent - 100;
          final isFromCurrentUser = newMessage.userId == _currentUser?.id;

          if (isNearBottom || isFromCurrentUser) {
            print('📜 Scrolling to bottom...');
            _scrollToBottomSmooth();
          }
        }
      } else {
        print('⚠️ Message already exists, updating status...');
        _messages[existingIndex] = newMessage.copyWith(
          messageStatus: MessageStatus.delivered,
        );
      }
    });
    print('🏁 _processNewMessage completed');
  }

  /// Clean up Socket.IO connections
  void _cleanupSocketConnections() {
    // Leave the chat room
    _chatSocketService.leaveChat(widget.chat.id.toString());

    // Cancel subscriptions
    _connectionSubscription?.cancel();
    _newMessageSubscription?.cancel();
    _errorSubscription?.cancel();

    // Remove only chat-specific listeners, preserve global listeners like 'chat_notification'
    _chatSocketService.removeChatSpecificListeners();
  }

  /// Handle message text changes to update UI
  void _onMessageTextChanged() {
    setState(() {
      // This will trigger a rebuild to update the send button state
    });
  }

  /// Send a message via Socket.IO with improved error handling
  void _sendMessage() {
    final content = _messageController.text.trim();

    // Validate input
    if (content.isEmpty) {
      _showErrorSnackBar('กรุณาพิมพ์ข้อความ');
      return;
    }

    if (_currentUser == null) {
      _showErrorSnackBar('ไม่พบข้อมูลผู้ใช้');
      return;
    }

    if (!_isSocketConnected) {
      _showErrorSnackBar('ไม่สามารถเชื่อมต่อได้ กรุณาลองใหม่อีกครั้ง');
      // Try to reconnect
      _initializeSocketConnection();
      return;
    }

    try {
      print('🚀 Attempting to send message: $content');
      print('📊 Socket connected: $_isSocketConnected');
      print('👤 User ID: ${_currentUser!.id}');
      print('💬 Chat ID: ${widget.chat.id}');

      // Create optimistic message for immediate UI update
      final optimisticMessage = _chatSocketService.createOptimisticMessage(
        content: content,
        messageType: MessageType.text,
        chatId: widget.chat.id,
        currentUser: _currentUser!,
      );

      // Add optimistic message to UI immediately
      setState(() {
        _messages.add(optimisticMessage);
      });

      // Clear the input field
      _messageController.clear();

      // Scroll to bottom
      _scrollToBottomSmooth();

      // Send message via Socket.IO with acknowledgment
      _chatSocketService.sendMessage(
        chatId: widget.chat.id.toString(),
        chatType: widget.chat.chatType.name,
        userId: _currentUser!.id,
        content: content,
        messageType: MessageType.text.name, // Send as lowercase "text"
        optimisticId: optimisticMessage.id.toString(),
        onAck: (response) {
          if (mounted) {
            print('📨 Message acknowledgment received: $response');
            if (response != null && response['success'] == true) {
              // Message acknowledged successfully
              // Note: We don't update status here anymore since _processNewMessage handles it
              // when we receive the new_message event from server
              print('✅ Server acknowledged message send');
            } else {
              // Message failed, update status
              print('❌ Server rejected message: $response');
              _handleMessageFailure(optimisticMessage, 'Server error');
            }
          }
        },
      );

      // Set timeout for failed message (15 seconds)
      Timer(const Duration(seconds: 15), () {
        if (mounted) {
          final messageIndex = _messages.indexWhere(
            (m) =>
                m.id == optimisticMessage.id &&
                m.messageStatus == MessageStatus.sending,
          );

          if (messageIndex != -1) {
            print('⏰ Message timeout for: ${optimisticMessage.content}');
            _handleMessageFailure(optimisticMessage, 'Connection timeout');
          }
        }
      });
    } catch (e) {
      print('❌ Error sending message: $e');
      _showErrorSnackBar('เกิดข้อผิดพลาดในการส่งข้อความ');
    }
  }

  /// Handle message read status updates
  void _handleMessagesRead(Map<String, dynamic> data) {
    // Update read status for messages in the current chat
    // This could be used to show read receipts
    print('Messages read update: $data');
  }

  /// Handle chat notifications
  void _handleChatNotification(Map<String, dynamic> data) {
    // Handle various chat notifications like user typing, etc.
    print('Chat notification: $data');
  }

  /// Show error message to user
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'ปิด',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  /// Handle message failure
  void _handleMessageFailure(ChatMessage failedMessage, String reason) {
    if (mounted) {
      final messageIndex = _messages.indexWhere(
        (m) => m.id == failedMessage.id,
      );

      if (messageIndex != -1) {
        setState(() {
          _messages[messageIndex] = _messages[messageIndex].copyWith(
            messageStatus: MessageStatus.failed,
          );
        });
      }

      print('❌ Message failed: ${failedMessage.content} - Reason: $reason');
      _showErrorSnackBar('ส่งข้อความไม่สำเร็จ: $reason');
    }
  }

  /// Retry sending a failed message
  void _retryFailedMessage(ChatMessage failedMessage) {
    if (_currentUser == null || !_isSocketConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('ไม่สามารถส่งข้อความได้ กรุณาตรวจสอบการเชื่อมต่อ'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Update message status to sending
    setState(() {
      final messageIndex = _messages.indexWhere(
        (m) => m.id == failedMessage.id,
      );
      if (messageIndex != -1) {
        _messages[messageIndex] = _messages[messageIndex].copyWith(
          messageStatus: MessageStatus.sending,
        );
      }
    });

    // Resend the message
    _chatSocketService.sendMessage(
      chatId: widget.chat.id.toString(),
      chatType: widget.chat.chatType.name,
      userId: _currentUser!.id,
      content: failedMessage.content,
      messageType: failedMessage.messageType.name.toUpperCase(),
      optimisticId: failedMessage.id.toString(),
    );

    // Set timeout for failed message retry
    Timer(const Duration(seconds: 10), () {
      if (mounted) {
        final messageIndex = _messages.indexWhere(
          (m) =>
              m.id == failedMessage.id &&
              m.messageStatus == MessageStatus.sending,
        );

        if (messageIndex != -1) {
          setState(() {
            _messages[messageIndex] = _messages[messageIndex].copyWith(
              messageStatus: MessageStatus.failed,
            );
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: FutureBuilder<String>(
          future: widget.chat.chatType == ChatType.private
              ? widget.chat.getPrivateChatDisplayName()
              : Future.value(widget.chat.displayName),
          builder: (context, snapshot) {
            final displayName = snapshot.data ?? widget.chat.displayName;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.chat.chatType.displayName,
                  style: const TextStyle(fontSize: 12, color: Colors.white70),
                ),
              ],
            );
          },
        ),
        actions: [
          // Connection status indicator in header
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: _isSocketConnected ? Colors.green : Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Text(
                  //   _isSocketConnected ? 'เชื่อมต่อ' : 'ออฟไลน์',
                  //   style: const TextStyle(fontSize: 12, color: Colors.white),
                  // ),
                ],
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _loadMessages(isRefresh: true),
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // Show chat info
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.grey[50]!, Colors.grey[100]!],
          ),
        ),
        child: Column(
          children: [
            // Messages List
            Expanded(child: _buildMessagesList()),

            // Message Input (Read-only for now)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(top: BorderSide(color: Colors.grey[200]!)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      enabled:
                          _isSocketConnected, // Enable when socket is connected
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) => _sendMessage(),
                      decoration: InputDecoration(
                        hintText: 'พิมพ์ข้อความ...',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  IconButton(
                    onPressed:
                        _isSocketConnected &&
                            _messageController.text.trim().isNotEmpty
                        ? _sendMessage
                        : null,
                    icon: const Icon(Icons.send),
                    style: IconButton.styleFrom(
                      backgroundColor: _messageController.text.trim().isNotEmpty
                          ? AppColors.primary
                          : Colors.grey[400],
                      foregroundColor: Colors.white,
                      shape: const CircleBorder(),
                      padding: const EdgeInsets.all(12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    if (_isLoading && _messages.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null && _messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'เกิดข้อผิดพลาด',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadMessages(isRefresh: true),
              child: const Text('ลองใหม่'),
            ),
          ],
        ),
      );
    }

    if (_messages.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'ไม่มีข้อความในแชทนี้',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadMessages(isRefresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        itemCount: _messages.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // Show loading indicator at the top for older messages
          if (_isLoadingMore && index == 0) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          // Adjust index if loading indicator is shown at top
          final messageIndex = _isLoadingMore ? index - 1 : index;
          final message = _messages[messageIndex];
          final isMyMessage = message.userId == _currentUserId;

          return _buildMessageBubble(message, isMyMessage);
        },
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, bool isMyMessage) {
    final isTextMessage = message.messageType == MessageType.text;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: isMyMessage
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // User name (only for others, outside balloon)
          if (!isMyMessage && message.user != null)
            Padding(
              padding: EdgeInsets.only(
                left: isTextMessage ? 50 : 50,
                bottom: 4,
              ),
              child: Text(
                message.user!.fullName,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
            ),

          // Message row with avatar and content
          Row(
            mainAxisAlignment: isMyMessage
                ? MainAxisAlignment.end
                : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (!isMyMessage) ...[
                CircleAvatar(
                  radius: 18,
                  backgroundColor: AppColors.primary,
                  child: Text(
                    message.user?.firstName.isNotEmpty == true
                        ? message.user!.firstName[0].toUpperCase()
                        : '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
              ],
              // Add flexible space on the left for my messages to push them right
              if (isMyMessage) const Expanded(flex: 1, child: SizedBox()),

              // Message content - with or without balloon based on message type
              Flexible(
                flex: isMyMessage ? 3 : 4,
                child: isTextMessage
                    ? _buildTextMessageBalloon(message, isMyMessage)
                    : _buildMediaMessageContent(message, isMyMessage),
              ),
            ],
          ),

          // Timestamp and read status (outside balloon)
          Padding(
            padding: EdgeInsets.only(
              top: 4,
              left: isMyMessage ? 0 : 50,
              right: isMyMessage ? 12 : 0,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: isMyMessage
                  ? MainAxisAlignment.end
                  : MainAxisAlignment.start,
              children: [
                Text(
                  message.timeDisplay,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[500],
                    fontWeight: FontWeight.w400,
                  ),
                ),
                if (isMyMessage) ...[
                  const SizedBox(width: 6),
                  _buildMessageStatusIcon(message),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextMessageBalloon(ChatMessage message, bool isMyMessage) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isMyMessage
            ? const Color(0xFF00C851) // Modern green for my messages
            : Colors.white, // White background for others
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
          bottomLeft: isMyMessage
              ? const Radius.circular(20)
              : const Radius.circular(4),
          bottomRight: isMyMessage
              ? const Radius.circular(4)
              : const Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: MessageContentWidget(message: message, isMyMessage: isMyMessage),
    );
  }

  Widget _buildMediaMessageContent(ChatMessage message, bool isMyMessage) {
    return MessageContentWidget(message: message, isMyMessage: isMyMessage);
  }

  /// Build message status icon based on message status
  Widget _buildMessageStatusIcon(ChatMessage message) {
    switch (message.messageStatus) {
      case MessageStatus.sending:
        return SizedBox(
          width: 14,
          height: 14,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[500]!),
          ),
        );
      case MessageStatus.delivered:
        return Icon(Icons.check, size: 14, color: Colors.grey[500]);
      case MessageStatus.read:
        return Icon(Icons.done_all, size: 14, color: Colors.blue[500]);
      case MessageStatus.failed:
        return GestureDetector(
          onTap: () => _retryFailedMessage(message),
          child: Icon(Icons.error_outline, size: 14, color: Colors.red[500]),
        );
    }
  }
}
