import 'dart:async';
import 'dart:math';
import 'package:hia_sang_ma/services/socket_service.dart';
import 'package:hia_sang_ma/services/unread_message_service.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/models/user_model.dart';

/// Chat-specific Socket.IO service for handling real-time chat events
class ChatSocketService {
  final SocketService _socketService = SocketService();
  final UnreadMessageService _unreadMessageService = UnreadMessageService();

  /// Stream controllers for different chat events
  final StreamController<ChatMessage> _newMessageController =
      StreamController<ChatMessage>.broadcast();
  final StreamController<Map<String, dynamic>> _chatNotificationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _messagesReadController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _userJoinedController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _userLeftController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _unreadCountController =
      StreamController<Map<String, dynamic>>.broadcast();

  /// Stream getters
  Stream<ChatMessage> get newMessageStream => _newMessageController.stream;
  Stream<Map<String, dynamic>> get chatNotificationStream =>
      _chatNotificationController.stream;
  Stream<Map<String, dynamic>> get messagesReadStream =>
      _messagesReadController.stream;
  Stream<Map<String, dynamic>> get userJoinedStream =>
      _userJoinedController.stream;
  Stream<Map<String, dynamic>> get userLeftStream => _userLeftController.stream;
  Stream<Map<String, dynamic>> get unreadCountStream =>
      _unreadCountController.stream;

  /// Setup chat event listeners
  void setupChatEventListeners() {
    print('🎯 setupChatEventListeners called');
    // Listen for new messages
    _socketService.on('new_message', (data) {
      try {
        print('📥 *** NEW_MESSAGE EVENT RECEIVED ***');
        print('📦 Raw data: $data');
        final message = _parseNewMessage(data);
        if (message != null) {
          print('✅ Successfully parsed message: ${message.content}');
          print('📤 Adding message to stream...');
          _newMessageController.add(message);
          print('✅ Message added to stream successfully');
        } else {
          print('❌ Failed to parse message from data: $data');
        }
      } catch (e) {
        print('❌ Error parsing new message: $e');
        print('📦 Raw data: $data');
        print('📍 Stack trace: $e');
      }
    });

    print('🎯 new_message event listener registered successfully');

    // Listen for message read updates
    _socketService.on('messages_read', (data) {
      _handleMessagesRead(data);
      _messagesReadController.add(data);
    });

    // Listen for user join events
    _socketService.on('receive_join', (data) {
      _userJoinedController.add(data);
    });

    // Listen for user leave events
    _socketService.on('receive_leave', (data) {
      _userLeftController.add(data);
    });

    // Listen for unread count updates
    _socketService.on('unread_count_updated', (data) {
      _handleUnreadCountUpdate(data);
      _unreadCountController.add(data);
    });

    // Listen for chat notifications
    _socketService.on('chat_notification', (data) {
      print('📢 Chat notification received: $data');
      _chatNotificationController.add(data);
    });

    // Listen for general notifications
    _socketService.on('send_notification', (data) {
      // Handle general notifications if needed
      print('Received notification: $data');
    });
  }

  /// Parse new message data from socket
  ChatMessage? _parseNewMessage(dynamic data) {
    try {
      print('🔍 _parseNewMessage called with data: $data');

      if (data == null) {
        print('❌ Data is null');
        return null;
      }

      final messageData = data as Map<String, dynamic>;
      print('📦 Message data: $messageData');

      // Create user model from sender data
      UserModel? user;
      if (messageData['sender'] != null) {
        print('👤 Creating user from sender data...');
        final senderData = messageData['sender'] as Map<String, dynamic>;
        user = UserModel(
          id: _parseIntSafely(senderData['id']) ?? 0,
          email: senderData['email']?.toString() ?? '',
          firstName: senderData['firstName']?.toString() ?? '',
          lastName: senderData['lastName']?.toString() ?? '',
          role: senderData['role']?.toString() ?? 'member',
          phone: senderData['phone']?.toString(),
          imageUrl: senderData['imageUrl']?.toString(),
        );
        print('✅ User created: ${user.firstName} ${user.lastName}');
      } else {
        print('⚠️ No sender data found');
      }

      // Create chat message
      print('🔨 Creating ChatMessage...');
      print('📊 messageId: ${messageData['messageId']}');
      print('📊 chatId: ${messageData['chatId']}');
      print('📊 content: ${messageData['content']}');
      print('📊 messageType: ${messageData['messageType']}');
      print('📊 status: ${messageData['status']}');

      final chatMessage = ChatMessage(
        id: _parseIntSafely(messageData['messageId']) ?? 0,
        chatId: 0, // Server doesn't send chatId, will be set later
        userId: _parseIntSafely(messageData['sender']?['id']) ?? 0,
        content: messageData['content']?.toString() ?? '',
        messageType: MessageType.fromString(
          messageData['messageType']?.toString() ?? 'TEXT',
        ),
        messageStatus: MessageStatus.fromString(
          messageData['status']?.toString() ?? 'DELIVERED',
        ),
        createdAt:
            DateTime.tryParse(messageData['createdAt']?.toString() ?? '') ??
            DateTime.now(),
        updatedAt:
            DateTime.tryParse(messageData['createdAt']?.toString() ?? '') ??
            DateTime.now(),
        user: user,
        optimisticId: messageData['optimisticId']?.toString(),
      );

      print('✅ ChatMessage created successfully: ${chatMessage.content}');
      return chatMessage;
    } catch (e) {
      print('❌ Error parsing message data: $e');
      print('📦 Raw data received: $data');
      print('📍 Stack trace: $e');
      return null;
    }
  }

  /// Send a new message with acknowledgment - Updated to match server expectations
  void sendMessage({
    required String chatId,
    required String chatType, // Keep for internal use but don't send to server
    required int userId,
    required String content,
    required String messageType,
    String? optimisticId,
    Function(dynamic)? onAck,
  }) {
    // Format data exactly as server expects
    final data = {
      'chatId': int.tryParse(chatId) ?? int.parse(chatId), // Convert to int
      'userId': userId,
      'content': content,
      'messageType': messageType.toLowerCase(), // Convert to lowercase
      // Don't send chatType and optimisticId to server
    };

    print('📤 Sending message data to server: $data');

    // Emit with acknowledgment callback
    _socketService.emitWithAck('send_message', data, onAck);
  }

  /// Join a chat room
  void joinChat(String chatId) {
    _socketService.emit('join_chat', {'chat_id': chatId});
  }

  /// Leave a chat room
  void leaveChat(String chatId) {
    _socketService.emit('leave_chat', {'chat_id': chatId});
  }

  /// Mark messages as read
  void markMessagesAsRead({required String chatId, required int userId}) {
    final data = {'chatId': chatId, 'userId': userId};

    _socketService.emit('messages_read', data);
  }

  /// Delete a message
  void deleteMessage({required String chatId, required String messageId}) {
    final data = {'chatId': chatId, 'messageId': messageId};

    _socketService.emit('message_deleted', data);
  }

  /// Create optimistic message for immediate UI update
  ChatMessage createOptimisticMessage({
    required String content,
    required MessageType messageType,
    required int chatId,
    required UserModel currentUser,
  }) {
    final optimisticId =
        'optimistic_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';

    return ChatMessage(
      id: optimisticId.hashCode, // Use hash as temporary ID
      chatId: chatId,
      userId: currentUser.id,
      content: content,
      messageType: messageType,
      messageStatus: MessageStatus.sending,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      user: currentUser,
    );
  }

  /// Handle unread count updates from WebSocket
  void _handleUnreadCountUpdate(dynamic data) {
    try {
      if (data is Map<String, dynamic>) {
        final chatId = _parseIntSafely(data['chatId']);
        final unreadCount = _parseIntSafely(data['unreadCount']) ?? 0;
        final chatTypeString = data['chatType']?.toString();

        if (chatId != null) {
          if (chatTypeString != null) {
            final chatType = ChatType.fromString(chatTypeString);
            _unreadMessageService.updateUnreadCountForChatWithType(
              chatId,
              unreadCount,
              chatType,
            );
          } else {
            _unreadMessageService.updateUnreadCountForChat(chatId, unreadCount);
          }
        }
      }
    } catch (e) {
      print('Error handling unread count update: $e');
    }
  }

  /// Handle messages read events from WebSocket
  void _handleMessagesRead(dynamic data) {
    try {
      if (data is Map<String, dynamic>) {
        final chatId = _parseIntSafely(data['chatId']);
        final readCount = _parseIntSafely(data['readCount']) ?? 0;

        if (chatId != null && readCount > 0) {
          // Refresh the unread count for this chat
          _refreshUnreadCountForChat(chatId);
        }
      }
    } catch (e) {
      print('Error handling messages read: $e');
    }
  }

  /// Refresh unread count for a specific chat
  void _refreshUnreadCountForChat(int chatId) {
    // This will be called asynchronously to avoid blocking the UI
    Future.microtask(() async {
      try {
        final newCount = await _unreadMessageService.getUnreadCountForChat(
          chatId,
        );
        _unreadMessageService.updateUnreadCountForChat(chatId, newCount);
      } catch (e) {
        print('Error refreshing unread count for chat $chatId: $e');
      }
    });
  }

  /// Remove chat-specific event listeners (preserves global listeners like chat_notification)
  void removeChatSpecificListeners() {
    _socketService.off('new_message');
    _socketService.off('messages_read');
    _socketService.off('receive_join');
    _socketService.off('receive_leave');
    _socketService.off('unread_count_updated');
    _socketService.off('send_notification');
    // Note: 'chat_notification' is preserved for global chat notifications
  }

  /// Remove all event listeners (including global ones)
  void removeAllListeners() {
    _socketService.off('new_message');
    _socketService.off('messages_read');
    _socketService.off('receive_join');
    _socketService.off('receive_leave');
    _socketService.off('unread_count_updated');
    _socketService.off('chat_notification');
    _socketService.off('send_notification');
  }

  /// Dispose resources
  void dispose() {
    removeAllListeners();
    _newMessageController.close();
    _chatNotificationController.close();
    _messagesReadController.close();
    _userJoinedController.close();
    _userLeftController.close();
    _unreadCountController.close();
  }

  /// Helper method to safely parse integers
  static int? _parseIntSafely(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value);
    }
    return null;
  }
}
