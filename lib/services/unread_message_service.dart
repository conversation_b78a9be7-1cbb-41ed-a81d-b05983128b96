import 'dart:async';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/services/chat_service.dart';

/// Service for managing unread message counts with caching and real-time updates
class UnreadMessageService {
  static final UnreadMessageService _instance = UnreadMessageService._internal();
  factory UnreadMessageService() => _instance;
  UnreadMessageService._internal();

  final ChatService _chatService = ChatService();
  
  // Cache for unread counts
  final Map<int, int> _unreadCountsCache = {};
  final Map<ChatType, int> _unreadCountsByTypeCache = {};
  int _totalUnreadCount = 0;
  
  // Stream controllers for real-time updates
  final StreamController<Map<int, int>> _unreadCountsController = 
      StreamController<Map<int, int>>.broadcast();
  final StreamController<Map<ChatType, int>> _unreadCountsByTypeController = 
      StreamController<Map<ChatType, int>>.broadcast();
  final StreamController<int> _totalUnreadCountController = 
      StreamController<int>.broadcast();
  final StreamController<UnreadCountUpdate> _unreadCountUpdateController = 
      StreamController<UnreadCountUpdate>.broadcast();

  // Getters for streams
  Stream<Map<int, int>> get unreadCountsStream => _unreadCountsController.stream;
  Stream<Map<ChatType, int>> get unreadCountsByTypeStream => _unreadCountsByTypeController.stream;
  Stream<int> get totalUnreadCountStream => _totalUnreadCountController.stream;
  Stream<UnreadCountUpdate> get unreadCountUpdateStream => _unreadCountUpdateController.stream;

  // Cache expiry
  DateTime? _lastCacheUpdate;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Initialize the service and load initial data
  Future<void> initialize() async {
    await refreshAllUnreadCounts();
  }

  /// Get unread count for a specific chat (with caching)
  Future<int> getUnreadCountForChat(int chatId) async {
    // Check cache first
    if (_unreadCountsCache.containsKey(chatId) && _isCacheValid()) {
      return _unreadCountsCache[chatId] ?? 0;
    }

    try {
      final count = await _chatService.getUnreadMessageCountForChat(chatId);
      _unreadCountsCache[chatId] = count;
      return count;
    } catch (e) {
      print('Error getting unread count for chat $chatId: $e');
      return _unreadCountsCache[chatId] ?? 0;
    }
  }

  /// Get unread count for a chat type (with caching)
  int getUnreadCountForType(ChatType chatType) {
    return _unreadCountsByTypeCache[chatType] ?? 0;
  }

  /// Get total unread count (with caching)
  int getTotalUnreadCount() {
    return _totalUnreadCount;
  }

  /// Get all unread counts (with caching)
  Map<int, int> getAllUnreadCounts() {
    return Map.from(_unreadCountsCache);
  }

  /// Get unread counts by type (with caching)
  Map<ChatType, int> getUnreadCountsByType() {
    return Map.from(_unreadCountsByTypeCache);
  }

  /// Refresh all unread counts from the server
  Future<void> refreshAllUnreadCounts() async {
    try {
      // Get all unread counts
      final unreadCounts = await _chatService.getAllUnreadMessageCounts();
      
      // Update cache
      _unreadCountsCache.clear();
      for (final unreadInfo in unreadCounts) {
        _unreadCountsCache[unreadInfo.chatId] = unreadInfo.unreadCount;
      }

      // Update counts by type
      _unreadCountsByTypeCache.clear();
      for (final chatType in ChatType.values) {
        _unreadCountsByTypeCache[chatType] = 0;
      }
      
      for (final unreadInfo in unreadCounts) {
        _unreadCountsByTypeCache[unreadInfo.chatType] = 
            (_unreadCountsByTypeCache[unreadInfo.chatType] ?? 0) + unreadInfo.unreadCount;
      }

      // Update total count
      _totalUnreadCount = _unreadCountsCache.values.fold(0, (sum, count) => sum + count);

      // Update cache timestamp
      _lastCacheUpdate = DateTime.now();

      // Notify listeners
      _unreadCountsController.add(Map.from(_unreadCountsCache));
      _unreadCountsByTypeController.add(Map.from(_unreadCountsByTypeCache));
      _totalUnreadCountController.add(_totalUnreadCount);

    } catch (e) {
      print('Error refreshing unread counts: $e');
    }
  }

  /// Update unread count for a specific chat (for real-time updates)
  void updateUnreadCountForChat(int chatId, int newCount) {
    final oldCount = _unreadCountsCache[chatId] ?? 0;
    _unreadCountsCache[chatId] = newCount;

    // Update total count
    _totalUnreadCount = _totalUnreadCount - oldCount + newCount;

    // We need to know the chat type to update type counts
    // This will be handled by the real-time update with chat type info
    
    // Notify listeners
    _unreadCountsController.add(Map.from(_unreadCountsCache));
    _totalUnreadCountController.add(_totalUnreadCount);
    _unreadCountUpdateController.add(UnreadCountUpdate(
      chatId: chatId,
      oldCount: oldCount,
      newCount: newCount,
    ));
  }

  /// Update unread count for a specific chat with chat type (for real-time updates)
  void updateUnreadCountForChatWithType(int chatId, int newCount, ChatType chatType) {
    final oldCount = _unreadCountsCache[chatId] ?? 0;
    _unreadCountsCache[chatId] = newCount;

    // Update total count
    _totalUnreadCount = _totalUnreadCount - oldCount + newCount;

    // Update type count
    final oldTypeCount = _unreadCountsByTypeCache[chatType] ?? 0;
    _unreadCountsByTypeCache[chatType] = oldTypeCount - oldCount + newCount;

    // Notify listeners
    _unreadCountsController.add(Map.from(_unreadCountsCache));
    _unreadCountsByTypeController.add(Map.from(_unreadCountsByTypeCache));
    _totalUnreadCountController.add(_totalUnreadCount);
    _unreadCountUpdateController.add(UnreadCountUpdate(
      chatId: chatId,
      oldCount: oldCount,
      newCount: newCount,
      chatType: chatType,
    ));
  }

  /// Mark messages as read and update cache
  Future<void> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) async {
    try {
      await _chatService.markMessagesAsRead(
        messageId: messageId,
        chatId: chatId,
        beforeMessageId: beforeMessageId,
      );

      // If marking all messages in a chat as read, update cache
      if (chatId != null && messageId == null) {
        updateUnreadCountForChat(chatId, 0);
      } else {
        // For specific message marking, refresh the count for that chat
        if (chatId != null) {
          final newCount = await _chatService.getUnreadMessageCountForChat(chatId);
          updateUnreadCountForChat(chatId, newCount);
        }
      }
    } catch (e) {
      print('Error marking messages as read: $e');
      rethrow;
    }
  }

  /// Check if cache is still valid
  bool _isCacheValid() {
    if (_lastCacheUpdate == null) return false;
    return DateTime.now().difference(_lastCacheUpdate!) < _cacheExpiry;
  }

  /// Clear cache and refresh
  Future<void> clearCacheAndRefresh() async {
    _unreadCountsCache.clear();
    _unreadCountsByTypeCache.clear();
    _totalUnreadCount = 0;
    _lastCacheUpdate = null;
    await refreshAllUnreadCounts();
  }

  /// Dispose resources
  void dispose() {
    _unreadCountsController.close();
    _unreadCountsByTypeController.close();
    _totalUnreadCountController.close();
    _unreadCountUpdateController.close();
  }
}

/// Model for unread count updates
class UnreadCountUpdate {
  final int chatId;
  final int oldCount;
  final int newCount;
  final ChatType? chatType;

  UnreadCountUpdate({
    required this.chatId,
    required this.oldCount,
    required this.newCount,
    this.chatType,
  });

  int get difference => newCount - oldCount;

  @override
  String toString() {
    return 'UnreadCountUpdate{chatId: $chatId, oldCount: $oldCount, newCount: $newCount, chatType: $chatType}';
  }
}
