import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:hia_sang_ma/services/unread_message_service.dart';
import 'package:hia_sang_ma/services/chat_service.dart';
import 'package:hia_sang_ma/models/chat_model.dart';

// Generate mocks
@GenerateMocks([ChatService])
import 'unread_message_service_test.mocks.dart';

void main() {
  group('UnreadMessageService', () {
    late UnreadMessageService unreadMessageService;
    late MockChatService mockChatService;

    setUp(() {
      mockChatService = MockChatService();
      unreadMessageService = UnreadMessageService();
      // We would need to inject the mock service in a real implementation
      // For now, we'll test the public interface
    });

    tearDown(() {
      unreadMessageService.dispose();
    });

    group('Initialization', () {
      test('should initialize with empty counts', () {
        expect(unreadMessageService.getTotalUnreadCount(), equals(0));
        expect(unreadMessageService.getUnreadCountForType(ChatType.private), equals(0));
        expect(unreadMessageService.getUnreadCountForType(ChatType.task), equals(0));
        expect(unreadMessageService.getUnreadCountForType(ChatType.department), equals(0));
        expect(unreadMessageService.getUnreadCountForType(ChatType.organization), equals(0));
      });

      test('should initialize all chat types in counts map', () {
        final countsByType = unreadMessageService.getUnreadCountsByType();
        expect(countsByType.keys.length, equals(ChatType.values.length));
        for (final chatType in ChatType.values) {
          expect(countsByType.containsKey(chatType), isTrue);
          expect(countsByType[chatType], equals(0));
        }
      });
    });

    group('Unread Count Updates', () {
      test('should update unread count for specific chat', () {
        const chatId = 123;
        const unreadCount = 5;

        unreadMessageService.updateUnreadCountForChat(chatId, unreadCount);

        expect(unreadMessageService.getAllUnreadCounts()[chatId], equals(unreadCount));
        expect(unreadMessageService.getTotalUnreadCount(), equals(unreadCount));
      });

      test('should update unread count with chat type', () {
        const chatId = 123;
        const unreadCount = 5;
        const chatType = ChatType.private;

        unreadMessageService.updateUnreadCountForChatWithType(chatId, unreadCount, chatType);

        expect(unreadMessageService.getAllUnreadCounts()[chatId], equals(unreadCount));
        expect(unreadMessageService.getUnreadCountForType(chatType), equals(unreadCount));
        expect(unreadMessageService.getTotalUnreadCount(), equals(unreadCount));
      });

      test('should handle multiple chat updates correctly', () {
        unreadMessageService.updateUnreadCountForChatWithType(123, 5, ChatType.private);
        unreadMessageService.updateUnreadCountForChatWithType(456, 3, ChatType.task);
        unreadMessageService.updateUnreadCountForChatWithType(789, 2, ChatType.private);

        expect(unreadMessageService.getTotalUnreadCount(), equals(10));
        expect(unreadMessageService.getUnreadCountForType(ChatType.private), equals(7));
        expect(unreadMessageService.getUnreadCountForType(ChatType.task), equals(3));
        expect(unreadMessageService.getUnreadCountForType(ChatType.department), equals(0));
      });

      test('should update existing chat count correctly', () {
        const chatId = 123;
        const chatType = ChatType.private;

        // Initial count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 5, chatType);
        expect(unreadMessageService.getTotalUnreadCount(), equals(5));
        expect(unreadMessageService.getUnreadCountForType(chatType), equals(5));

        // Update count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 8, chatType);
        expect(unreadMessageService.getTotalUnreadCount(), equals(8));
        expect(unreadMessageService.getUnreadCountForType(chatType), equals(8));

        // Reduce count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 2, chatType);
        expect(unreadMessageService.getTotalUnreadCount(), equals(2));
        expect(unreadMessageService.getUnreadCountForType(chatType), equals(2));
      });

      test('should handle zero counts correctly', () {
        const chatId = 123;
        const chatType = ChatType.private;

        // Set initial count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 5, chatType);
        expect(unreadMessageService.getTotalUnreadCount(), equals(5));

        // Set to zero
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 0, chatType);
        expect(unreadMessageService.getTotalUnreadCount(), equals(0));
        expect(unreadMessageService.getUnreadCountForType(chatType), equals(0));
      });
    });

    group('Stream Updates', () {
      test('should emit updates on unread count changes', () async {
        const chatId = 123;
        const unreadCount = 5;
        const chatType = ChatType.private;

        // Listen to the stream
        final streamFuture = unreadMessageService.unreadCountUpdateStream.first;

        // Update count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, unreadCount, chatType);

        // Verify stream emission
        final update = await streamFuture;
        expect(update.chatId, equals(chatId));
        expect(update.newCount, equals(unreadCount));
        expect(update.oldCount, equals(0));
        expect(update.chatType, equals(chatType));
        expect(update.difference, equals(unreadCount));
      });

      test('should emit correct difference in updates', () async {
        const chatId = 123;
        const chatType = ChatType.private;

        // Set initial count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 5, chatType);

        // Listen to the stream for the next update
        final streamFuture = unreadMessageService.unreadCountUpdateStream.first;

        // Update count
        unreadMessageService.updateUnreadCountForChatWithType(chatId, 8, chatType);

        // Verify stream emission
        final update = await streamFuture;
        expect(update.chatId, equals(chatId));
        expect(update.newCount, equals(8));
        expect(update.oldCount, equals(5));
        expect(update.difference, equals(3));
      });
    });

    group('UnreadCountUpdate Model', () {
      test('should calculate difference correctly', () {
        final update = UnreadCountUpdate(
          chatId: 123,
          oldCount: 5,
          newCount: 8,
          chatType: ChatType.private,
        );

        expect(update.difference, equals(3));
      });

      test('should handle negative difference', () {
        final update = UnreadCountUpdate(
          chatId: 123,
          oldCount: 8,
          newCount: 3,
          chatType: ChatType.private,
        );

        expect(update.difference, equals(-5));
      });

      test('should handle zero difference', () {
        final update = UnreadCountUpdate(
          chatId: 123,
          oldCount: 5,
          newCount: 5,
          chatType: ChatType.private,
        );

        expect(update.difference, equals(0));
      });

      test('should create string representation correctly', () {
        final update = UnreadCountUpdate(
          chatId: 123,
          oldCount: 5,
          newCount: 8,
          chatType: ChatType.private,
        );

        final string = update.toString();
        expect(string, contains('chatId: 123'));
        expect(string, contains('oldCount: 5'));
        expect(string, contains('newCount: 8'));
        expect(string, contains('chatType: ChatType.private'));
      });
    });
  });
}
